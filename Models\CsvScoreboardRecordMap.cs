using CsvHelper.Configuration;
using ScoreboardCsvIngestor.Services;

namespace ScoreboardCsvIngestor.Models;

/// <summary>
/// CsvHelper class map for CsvScoreboardRecord to specify custom converters
/// </summary>
public sealed class CsvScoreboardRecordMap : ClassMap<CsvScoreboardRecord>
{
    public CsvScoreboardRecordMap()
    {
        Map(m => m.StoreName).Index(0).Name("Store Name");
        Map(m => m.LaneDepartureTime).Index(1).Name("Lane Departure Time").TypeConverter<CustomDateTimeConverter>();
        Map(m => m.CarType).Index(2).Name("Car Type");
        Map(m => m.LaneNumber).Index(3).Name("Lane Number").TypeConverter<CustomIntegerConverter>();
        Map(m => m.OrderTime).Index(4).Name("Order Time").TypeConverter<CustomIntegerConverter>();
        Map(m => m.PickupTime).Index(5).Name("Pickup Time").TypeConverter<CustomIntegerConverter>();
        Map(m => m.QueueTime).Index(6).Name("Queue Time").TypeConverter<CustomIntegerConverter>();
        Map(m => m.TotalTime).Index(7).Name("Total Time").TypeConverter<CustomIntegerConverter>();
    }
}
