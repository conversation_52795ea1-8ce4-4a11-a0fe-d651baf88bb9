using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Extensions.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ScoreboardCsvIngestor.Configuration;
using ScoreboardCsvIngestor.Data;
using ScoreboardCsvIngestor.Services;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureServices((context, services) =>
    {
        // Get app settings
        var appSettings = context.Configuration.GetAppSettings();
        services.AddSingleton(appSettings);

        // Add Application Insights
        if (!string.IsNullOrEmpty(appSettings.ApplicationInsightsConnectionString))
        {
            services.AddApplicationInsightsTelemetryWorkerService();
            services.ConfigureFunctionsApplicationInsights();
        }

        // Add Entity Framework
        services.AddDbContext<ScoreboardDbContext>(options =>
        {
            options.UseSqlServer(appSettings.SqlConnectionString);
            if (appSettings.EnableDetailedLogging)
            {
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            }
        });

        // Add custom services
        services.AddScoped<ICsvParserService, CsvParserService>();
        services.AddScoped<IScoreboardDataService, ScoreboardDataService>();

        // Configure logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            if (!string.IsNullOrEmpty(appSettings.ApplicationInsightsConnectionString))
            {
                builder.AddApplicationInsights();
            }

            if (appSettings.EnableDetailedLogging)
            {
                builder.SetMinimumLevel(LogLevel.Debug);
            }
        });
    })
    .Build();

// Ensure database is created on startup
await EnsureDatabaseCreatedAsync(host);

host.Run();

static async Task EnsureDatabaseCreatedAsync(IHost host)
{
    using var scope = host.Services.CreateScope();
    var context = scope.ServiceProvider.GetRequiredService<ScoreboardDbContext>();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

    try
    {
        logger.LogInformation("Ensuring database schema exists...");
        await context.Database.EnsureCreatedAsync();
        logger.LogInformation("Database schema verification completed");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Failed to ensure database schema exists");
        // Don't throw - let the function start anyway
    }
}
