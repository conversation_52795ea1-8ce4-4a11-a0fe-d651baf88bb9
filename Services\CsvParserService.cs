using CsvHelper;
using CsvHelper.Configuration;
using CsvHelper.TypeConversion;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using ScoreboardCsvIngestor.Models;
using System.ComponentModel.DataAnnotations;
using System.Globalization;
using System.Text;

namespace ScoreboardCsvIngestor.Services;

public class CsvParserService : ICsvParserService
{
    private readonly ILogger<CsvParserService> _logger;
    private readonly IConfiguration _configuration;
    private readonly int _maxRows;

    public CsvParserService(ILogger<CsvParserService> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
        _maxRows = _configuration.GetValue<int>("MaxCsvRows", 500);
    }

    public async Task<CsvParseResult> ParseCsvAsync(Stream csvStream)
    {
        try
        {
            using var reader = new StreamReader(csvStream, Encoding.UTF8);
            var csvContent = await reader.ReadToEndAsync();
            return await ParseCsvAsync(csvContent);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error reading CSV stream");
            return new CsvParseResult
            {
                IsSuccess = false,
                Errors = new List<string> { $"Error reading CSV stream: {ex.Message}" }
            };
        }
    }

    public async Task<CsvParseResult> ParseCsvAsync(string csvContent)
    {
        var result = new CsvParseResult();
        
        try
        {
            _logger.LogInformation("Starting CSV parsing process");

            if (string.IsNullOrWhiteSpace(csvContent))
            {
                result.Errors.Add("CSV content is empty or null");
                return result;
            }

            // Debug logging - log the first few lines of CSV content
            var lines = csvContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
            _logger.LogInformation("CSV content has {LineCount} lines", lines.Length);
            for (int i = 0; i < Math.Min(3, lines.Length); i++)
            {
                _logger.LogInformation("CSV Line {LineNumber}: '{LineContent}'", i + 1, lines[i]);
            }

            var config = new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                MissingFieldFound = null,
                HeaderValidated = null,
                TrimOptions = TrimOptions.Trim,
                BadDataFound = context =>
                {
                    _logger.LogWarning("Bad data found at row {Row}, field {Field}: {RawRecord}",
                        context.Context.Parser.Row, context.Field, context.RawRecord);
                }
            };

            using var stringReader = new StringReader(csvContent);
            using var csv = new CsvReader(stringReader, config);

            // Register the class map that specifies custom converters
            csv.Context.RegisterClassMap<CsvScoreboardRecordMap>();

            var records = new List<CsvScoreboardRecord>();
            var rowNumber = 1; // Start at 1 for header

            await foreach (var record in csv.GetRecordsAsync<CsvScoreboardRecord>())
            {
                rowNumber++;
                result.TotalRowsProcessed++;

                // Check row limit
                if (result.TotalRowsProcessed > _maxRows)
                {
                    result.Errors.Add($"CSV file exceeds maximum allowed rows ({_maxRows}). Processing stopped at row {rowNumber}.");
                    break;
                }

                // Validate the record
                var validationErrors = ValidateRecord(record, rowNumber);
                if (validationErrors.Any())
                {
                    result.ValidationErrors.AddRange(validationErrors);
                    result.InvalidRowsCount++;
                    _logger.LogWarning("Validation errors found for row {RowNumber}: {Errors}", 
                        rowNumber, string.Join(", ", validationErrors.Select(e => e.ErrorMessage)));
                }
                else
                {
                    records.Add(record);
                    result.ValidRowsCount++;
                }
            }

            result.Records = records;
            result.IsSuccess = result.ValidRowsCount > 0 && result.Errors.Count == 0;

            _logger.LogInformation("CSV parsing completed. Total rows: {Total}, Valid: {Valid}, Invalid: {Invalid}", 
                result.TotalRowsProcessed, result.ValidRowsCount, result.InvalidRowsCount);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing CSV content");
            result.IsSuccess = false;
            result.Errors.Add($"Error parsing CSV: {ex.Message}");
            return result;
        }
    }

    private List<ValidationError> ValidateRecord(CsvScoreboardRecord record, int rowNumber)
    {
        var errors = new List<ValidationError>();
        var validationContext = new ValidationContext(record);
        var validationResults = new List<ValidationResult>();

        if (!Validator.TryValidateObject(record, validationContext, validationResults, true))
        {
            foreach (var validationResult in validationResults)
            {
                var fieldName = validationResult.MemberNames.FirstOrDefault() ?? "Unknown";
                errors.Add(new ValidationError
                {
                    RowNumber = rowNumber,
                    FieldName = fieldName,
                    ErrorMessage = validationResult.ErrorMessage ?? "Validation error",
                    InvalidValue = GetFieldValue(record, fieldName)?.ToString()
                });
            }
        }

        // Additional business logic validation
        if (record.LaneNumber <= 0)
        {
            errors.Add(new ValidationError
            {
                RowNumber = rowNumber,
                FieldName = nameof(record.LaneNumber),
                ErrorMessage = "Lane Number must be greater than 0",
                InvalidValue = record.LaneNumber.ToString()
            });
        }

        return errors;
    }

    private object? GetFieldValue(CsvScoreboardRecord record, string fieldName)
    {
        var property = typeof(CsvScoreboardRecord).GetProperty(fieldName);
        return property?.GetValue(record);
    }
}

/// <summary>
/// Custom DateTime converter to handle the specific date format in the CSV files
/// </summary>
public class CustomDateTimeConverter : CsvHelper.TypeConversion.DateTimeConverter
{
    public override object ConvertFromString(string text, CsvHelper.IReaderRow row, CsvHelper.Configuration.MemberMapData memberMapData)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            return DateTime.MinValue;
        }

        // Try multiple date formats that might be in the CSV
        var formats = new[]
        {
            "dd/MM/yyyy h:mm tt",      // 25/06/2025 6:43 AM
            "dd/MM/yyyy HH:mm",        // 25/06/2025 18:43
            "dd/MM/yyyy",              // 25/06/2025
            "MM/dd/yyyy h:mm tt",      // 06/25/2025 6:43 AM
            "MM/dd/yyyy HH:mm",        // 06/25/2025 18:43
            "MM/dd/yyyy",              // 06/25/2025
            "yyyy-MM-dd HH:mm:ss",     // 2025-06-25 18:43:00
            "yyyy-MM-dd",              // 2025-06-25
        };

        foreach (var format in formats)
        {
            if (DateTime.TryParseExact(text.Trim(), format, CultureInfo.InvariantCulture, DateTimeStyles.None, out var result))
            {
                return result;
            }
        }

        // If none of the specific formats work, try the default parsing
        if (DateTime.TryParse(text.Trim(), out var defaultResult))
        {
            return defaultResult;
        }

        throw new CsvHelper.TypeConversion.TypeConverterException(this, memberMapData, text, row.Context,
            $"Unable to convert '{text}' to DateTime. Expected formats: {string.Join(", ", formats)}");
    }
}

/// <summary>
/// Custom Integer converter to handle numbers with comma separators (e.g., "1,010")
/// </summary>
public class CustomIntegerConverter : CsvHelper.TypeConversion.Int32Converter
{
    public override object ConvertFromString(string text, CsvHelper.IReaderRow row, CsvHelper.Configuration.MemberMapData memberMapData)
    {
        if (string.IsNullOrWhiteSpace(text))
        {
            return 0;
        }

        // Remove commas and any extra whitespace from the text before parsing
        var cleanText = text.Trim().Replace(",", "").Replace(" ", "");

        // Handle negative numbers
        if (int.TryParse(cleanText, NumberStyles.Integer, CultureInfo.InvariantCulture, out var result))
        {
            return result;
        }

        // If standard parsing fails, try with AllowThousands to handle any remaining comma scenarios
        if (int.TryParse(text.Trim(), NumberStyles.AllowThousands | NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out var resultWithCommas))
        {
            return resultWithCommas;
        }

        throw new CsvHelper.TypeConversion.TypeConverterException(this, memberMapData, text, row.Context,
            $"Unable to convert '{text}' to Int32. Expected a valid integer (commas are allowed as thousands separators).");
    }
}
